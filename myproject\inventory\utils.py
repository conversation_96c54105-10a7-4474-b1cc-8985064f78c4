import joblib
import os
import pandas as pd
from django.conf import settings
from inventory.models import Inventory, FactoryPart

# Load the model
MODEL_PATH = os.path.join(settings.BASE_DIR, 'inventory', 'ml_models', 'transfer_model.pkl')
model = joblib.load(MODEL_PATH)

def predict_transfer(factory, part):
    try:
        inventory = Inventory.objects.get(factory=factory, part=part)
        fp = FactoryPart.objects.get(factory=factory, part=part)
        supplier_links = FactoryPart.objects.filter(part=part).exclude(factory=factory)

        # Extracting features
        current_stock = inventory.current_quantity
        target_quantity = fp.target_quantity
        stock_deficit = max(0, target_quantity - current_stock)
        supplier_count = supplier_links.count()
        lead_times = [s.lead_time_days for s in supplier_links if s.lead_time_days is not None]
        capacities = [s.manufacturing_capacity_per_day for s in supplier_links if s.manufacturing_capacity_per_day is not None]
        
        fastest_lead_time = min(lead_times) if lead_times else 0
        max_supplier_capacity = max(capacities) if capacities else 0

        features = pd.DataFrame([{
            'current_stock': current_stock,
            'target_quantity': target_quantity,
            'stock_deficit': stock_deficit,
            'supplier_count': supplier_count,
            'fastest_lead_time': fastest_lead_time,
            'max_supplier_capacity': max_supplier_capacity,
        }])

        prediction = model.predict(features)[0]
        return prediction

    except Inventory.DoesNotExist:
        return "Error: Inventory record not found."
    except FactoryPart.DoesNotExist:
        return "Error: FactoryPart mapping not found."
    except Exception as e:
        return f"Unexpected error: {e}"
