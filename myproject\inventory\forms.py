from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth import get_user_model
from .models import Factory, Part, FactoryPart, Inventory

User = get_user_model()

class SimpleUserCreationForm(forms.Form):
    """
    A completely custom user creation form with role selection and no password validation
    """
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Choose a username'
        })
    )

    password1 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter password'
        }),
        label="Password"
    )

    password2 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm password'
        }),
        label="Confirm Password"
    )

    # Only allow the three specific roles
    ROLE_CHOICES = [
        ('superuser', 'Admin'),
        ('manager', 'Manager'),
        ('staff', 'Employee')
    ]

    role = forms.ChoiceField(
        choices=ROLE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'form-control'
        }),
        label="Role",
        initial='staff'  # Default to employee role
    )

    def clean_username(self):
        username = self.cleaned_data.get('username')
        if User.objects.filter(username=username).exists():
            raise forms.ValidationError("This username is already taken.")
        return username

    def clean(self):
        cleaned_data = super().clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')

        if password1 and password2 and password1 != password2:
            self.add_error('password2', "The two passwords don't match.")

        # Limit the total number of users to 3 (one for each role)
        role = cleaned_data.get('role')
        if role and User.objects.filter(role=role).exists():
            self.add_error('role', f"A user with the role '{role}' already exists. Only one user per role is allowed.")

        return cleaned_data

    def save(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password1')
        role = self.cleaned_data.get('role')

        # Create a new user with the provided username, password and role
        user = User.objects.create_user(
            username=username,
            password=password
        )

        # Set the role
        user.role = role

        # If the role is superuser, make them a Django superuser as well
        if role == 'superuser':
            user.is_superuser = True
            user.is_staff = True

        user.save()
        return user

class FactoryForm(forms.ModelForm):
    class Meta:
        model = Factory
        fields = ['name', 'location']
        widgets = {
            'name': forms.TextInput(attrs={
                'placeholder': 'Enter factory name...',
                'class': 'form-control'
            }),
            'location': forms.TextInput(attrs={
                'placeholder': 'Enter location...',
                'class': 'form-control'
            }),
        }

class PartForm(forms.ModelForm):
    class Meta:
        model = Part
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={
                'placeholder': 'Enter part name...',
                'class': 'form-control'
            }),
            'description': forms.Textarea(attrs={
                'rows': 4,
                'placeholder': 'Enter part description...',
                'class': 'form-control'
            }),
        }

class FactoryPartForm(forms.ModelForm):
    class Meta:
        model = FactoryPart
        fields = [
            'factory',
            'part',
            'target_quantity',
            'manufacturing_capacity_per_day',
            'lead_time_days'
        ]
        widgets = {
            'factory': forms.Select(attrs={'class': 'form-control'}),
            'part': forms.Select(attrs={'class': 'form-control'}),
            'target_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Target quantity'
            }),
            'manufacturing_capacity_per_day': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Capacity per day'
            }),
            'lead_time_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Lead time in days'
            }),
        }

class InventoryForm(forms.ModelForm):
    class Meta:
        model = Inventory
        fields = ['factory', 'part', 'current_quantity']
        widgets = {
            'factory': forms.Select(attrs={'class': 'form-control'}),
            'part': forms.Select(attrs={'class': 'form-control'}),
            'current_quantity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter current quantity in stock'
            }),
        }
